'use client';
import Link from 'next/link';

interface UserStatsProps {
  username: string;
  stats: {
    uploads: number;
    comments: number;
    likes: number;
    favorites: number;
    tags: number;
    dislikes?: number;
  };
  joinDate?: string;
  lastSeen?: string;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  showMemberInfo?: boolean;
  showDislikes?: boolean;
  title?: string;
}

export function UserStats({
  username,
  stats,
  joinDate,
  lastSeen,
  className = '',
  size = 'medium',
  showMemberInfo = true,
  showDislikes = false,
  title = 'User Statistics'
}: UserStatsProps) {
  const sizeClasses = {
    small: {
      container: 'p-3 rounded-lg',
      grid: showDislikes ? 'grid-cols-6 gap-2' : 'grid-cols-5 gap-2',
      statBox: 'text-center p-2 rounded-md',
      number: 'text-sm font-semibold',
      label: 'text-[10px] uppercase tracking-wide',
      title: 'text-sm font-medium mb-2',
      memberInfo: 'text-xs'
    },
    medium: {
      container: 'p-4 rounded-xl',
      grid: showDislikes ? 'grid-cols-2 md:grid-cols-6 gap-3' : 'grid-cols-2 md:grid-cols-5 gap-3',
      statBox: 'text-center p-3 rounded-lg',
      number: 'text-lg font-bold',
      label: 'text-xs uppercase tracking-wide',
      title: 'text-base font-medium mb-3',
      memberInfo: 'text-sm'
    },
    large: {
      container: 'p-6 rounded-xl',
      grid: showDislikes ? 'grid-cols-2 md:grid-cols-6 gap-4' : 'grid-cols-2 md:grid-cols-5 gap-4',
      statBox: 'text-center p-3 rounded-lg',
      number: 'text-2xl font-bold',
      label: 'text-xs uppercase tracking-wide',
      title: 'text-lg font-medium mb-4',
      memberInfo: 'text-sm'
    }
  };

  const classes = sizeClasses[size];

  return (
    <div className={`${classes.container} bg-gray-50/80 dark:bg-gray-900/50 backdrop-blur-sm border border-gray-100 dark:border-gray-800 ${className}`}>
      <h3 className={`${classes.title} font-[family-name:var(--font-geist-mono)] text-gray-800 dark:text-gray-400`}>
        {title}
      </h3>
      
      <div className={`${classes.grid}`}>
        <Link 
          href={`/posts?uploader=${username}`} 
          className={`${classes.statBox} bg-white/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-colors cursor-pointer`}
        >
          <div className={`${classes.number} text-purple-600 dark:text-purple-400`}>
            {stats.uploads}
          </div>
          <div className={`${classes.label} text-gray-500 dark:text-gray-400`}>
            Uploads
          </div>
        </Link>
        
        <Link 
          href={`/comments?author=${username}`} 
          className={`${classes.statBox} bg-white/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-colors cursor-pointer`}
        >
          <div className={`${classes.number} text-blue-600 dark:text-blue-400`}>
            {stats.comments}
          </div>
          <div className={`${classes.label} text-gray-500 dark:text-gray-400`}>
            Comments
          </div>
        </Link>
        
        <div className={`${classes.statBox} bg-white/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50`}>
          <div className={`${classes.number} text-green-600 dark:text-green-400`}>
            {stats.likes}
          </div>
          <div className={`${classes.label} text-gray-500 dark:text-gray-400`}>
            Likes
          </div>
        </div>
        
        <div className={`${classes.statBox} bg-white/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50`}>
          <div className={`${classes.number} text-pink-600 dark:text-pink-400`}>
            {stats.favorites}
          </div>
          <div className={`${classes.label} text-gray-500 dark:text-gray-400`}>
            Favorites
          </div>
        </div>
        
        <div className={`${classes.statBox} bg-white/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50`}>
          <div className={`${classes.number} text-orange-600 dark:text-orange-400`}>
            {stats.tags}
          </div>
          <div className={`${classes.label} text-gray-500 dark:text-gray-400`}>
            Tags Created
          </div>
        </div>

        {showDislikes && (
          <div className={`${classes.statBox} bg-white/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50`}>
            <div className={`${classes.number} text-red-600 dark:text-red-400`}>
              {stats.dislikes || 0}
            </div>
            <div className={`${classes.label} text-gray-500 dark:text-gray-400`}>
              Dislikes
            </div>
          </div>
        )}
      </div>
      
      {showMemberInfo && (joinDate || lastSeen) && (
        <div className="mt-4 pt-4 border-t border-gray-200/50 dark:border-gray-700/50">
          <div className="text-center">
            {joinDate && (
              <div className={`${classes.memberInfo} text-gray-600 dark:text-gray-400`}>
                Member since {new Date(joinDate).toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </div>
            )}
            {lastSeen && (
              <div className={`${classes.memberInfo} text-gray-500 dark:text-gray-500 mt-1`}>
                Last seen {new Date(lastSeen).toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'short', 
                  day: 'numeric' 
                })}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
